import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>y,
  IsO<PERSON>al,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsDate,
  IsUUID,
  ArrayMinSize,
  ArrayMaxSize,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>ested,
  IsNotEmpty
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { CreateExerciseQuestionDto, UpdateExerciseQuestionDto } from './exercise-question.dto';
import { EWorksheetQuestionStatus } from '../../mongodb/schemas/worksheet-question-document.schema';

/**
 * DTO for adding a question to a worksheet
 *
 * Interface structure:
 * ```typescript
 * interface AddQuestionToWorksheetDto {
 *   // Question Content (Required)
 *   type: 'multiple_choice' | 'true_false' | 'fill_in_the_blank' | 'short_answer' | 'essay' | 'matching' | 'ordering' | 'calculation' | 'diagram' | 'long_answer';
 *   content: string;
 *   options: string[];
 *   answer: string[];
 *   explain: string;
 *
 *   // Database Option References (Optional)
 *   optionTypeId?: string;  // UUID reference to OptionType entity
 *   optionValueId?: string; // UUID reference to OptionValue entity
 *
 *   // Worksheet-specific fields (Optional)
 *   position?: number;
 *   points?: number;
 *
 *   // Additional fields inherited from CreateExerciseQuestionDto...
 * }
 * ```
 */
export class AddQuestionToWorksheetDto extends CreateExerciseQuestionDto {
  @ApiPropertyOptional({
    description: 'Position in worksheet (if not provided, will be added at the end)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  position?: number;

  @ApiPropertyOptional({
    description: 'Points allocated for this question',
    minimum: 0,
    maximum: 100,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  points?: number;

  @ApiPropertyOptional({
    description: 'Database option type ID for categorizing the question context',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  optionTypeId?: string;

  @ApiPropertyOptional({
    description: 'Database option value ID for specific option selection within the type',
    example: '987fcdeb-51a2-43d1-b789-123456789abc'
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  optionValueId?: string;

  @ApiPropertyOptional({
    description: 'Question Pool ID - if provided, the question will be copied from the question pool instead of creating a new one',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString()
  questionPoolId?: string;

  // Note: status field is inherited from CreateExerciseQuestionDto
}

/**
 * DTO for updating a question in a worksheet (PATCH - partial updates)
 */
export class UpdateWorksheetQuestionDto extends PartialType(AddQuestionToWorksheetDto) {
  @ApiPropertyOptional({
    description: 'Version number for optimistic locking',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  version?: number;

  @ApiPropertyOptional({
    description: 'Reason for the update (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  updateReason?: string;
}

/**
 * DTO for full question replacement (PUT)
 */
export class ReplaceWorksheetQuestionDto extends AddQuestionToWorksheetDto {
  @ApiProperty({
    description: 'Version number for optimistic locking',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  version: number;

  @ApiPropertyOptional({
    description: 'Reason for the replacement (for audit purposes)'
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  updateReason?: string;
}

/**
 * DTO for reordering questions in a worksheet
 */
export class ReorderQuestionDto {
  @ApiProperty({ 
    description: 'Question ID to reorder' 
  })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiProperty({ 
    description: 'New position for the question',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  newPosition: number;
}

/**
 * DTO for bulk reordering questions
 */
export class BulkReorderQuestionsDto {
  @ApiProperty({ 
    description: 'Array of question reorder operations',
    type: [ReorderQuestionDto]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @ValidateNested({ each: true })
  @Type(() => ReorderQuestionDto)
  reorders: ReorderQuestionDto[];
}

/**
 * DTO for removing questions from worksheet
 */
export class RemoveQuestionsDto {
  @ApiProperty({ 
    description: 'Question IDs to remove',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  questionIds: string[];

  @ApiPropertyOptional({ 
    description: 'Reason for removal (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * DTO for updating question status in worksheet
 */
export class UpdateQuestionStatusDto {
  @ApiProperty({ 
    description: 'Question IDs to update',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  questionIds: string[];

  @ApiProperty({ 
    description: 'New status for the questions',
    enum: EWorksheetQuestionStatus
  })
  @IsEnum(EWorksheetQuestionStatus)
  status: EWorksheetQuestionStatus;

  @ApiPropertyOptional({ 
    description: 'Reason for status change (for audit purposes)' 
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  reason?: string;
}

/**
 * DTO for copying questions between worksheets
 */
export class CopyQuestionsDto {
  @ApiProperty({ 
    description: 'Source worksheet ID' 
  })
  @IsString()
  @IsNotEmpty()
  sourceWorksheetId: string;

  @ApiProperty({ 
    description: 'Question IDs to copy',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  questionIds: string[];

  @ApiPropertyOptional({ 
    description: 'Position to insert copied questions (if not provided, will be added at the end)',
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  insertPosition?: number;

  @ApiPropertyOptional({ 
    description: 'Whether to copy question metadata and settings' 
  })
  @IsOptional()
  @IsBoolean()
  copyMetadata?: boolean;
}

/**
 * DTO for moving questions between worksheets
 */
export class MoveQuestionsDto extends CopyQuestionsDto {
  @ApiPropertyOptional({ 
    description: 'Whether to preserve original question order in target worksheet' 
  })
  @IsOptional()
  @IsBoolean()
  preserveOrder?: boolean;
}

/**
 * DTO for worksheet question search and filtering
 */
export class WorksheetQuestionSearchDto {
  @ApiPropertyOptional({ 
    description: 'Worksheet ID to search within' 
  })
  @IsOptional()
  @IsString()
  worksheetId?: string;

  @ApiPropertyOptional({ 
    description: 'Question status filter',
    enum: EWorksheetQuestionStatus
  })
  @IsOptional()
  @IsEnum(EWorksheetQuestionStatus)
  status?: EWorksheetQuestionStatus;

  @ApiPropertyOptional({ 
    description: 'Minimum position' 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minPosition?: number;

  @ApiPropertyOptional({ 
    description: 'Maximum position' 
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxPosition?: number;

  @ApiPropertyOptional({ 
    description: 'Minimum points' 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minPoints?: number;

  @ApiPropertyOptional({ 
    description: 'Maximum points' 
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxPoints?: number;

  @ApiPropertyOptional({ 
    description: 'Created by user ID' 
  })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({ 
    description: 'Created after date' 
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  createdAfter?: Date;

  @ApiPropertyOptional({ 
    description: 'Created before date' 
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  createdBefore?: Date;

  @ApiPropertyOptional({ 
    description: 'Include locked questions' 
  })
  @IsOptional()
  @IsBoolean()
  includeLocked?: boolean;

  @ApiPropertyOptional({ 
    description: 'Page number for pagination',
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiPropertyOptional({ 
    description: 'Items per page',
    minimum: 1,
    maximum: 100,
    default: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @ApiPropertyOptional({ 
    description: 'Sort field',
    enum: ['position', 'createdAt', 'updatedAt', 'points']
  })
  @IsOptional()
  @IsEnum(['position', 'createdAt', 'updatedAt', 'points'])
  sortBy?: 'position' | 'createdAt' | 'updatedAt' | 'points';

  @ApiPropertyOptional({ 
    description: 'Sort order',
    enum: ['asc', 'desc']
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';
}

/**
 * DTO for question lock operations
 */
export class QuestionLockDto {
  @ApiProperty({ 
    description: 'Question ID to lock/unlock' 
  })
  @IsString()
  @IsNotEmpty()
  questionId: string;

  @ApiPropertyOptional({ 
    description: 'Lock duration in seconds',
    minimum: 60,
    maximum: 3600,
    default: 300
  })
  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(3600)
  lockDurationSeconds?: number;

  @ApiPropertyOptional({ 
    description: 'Reason for locking' 
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  reason?: string;
}

/**
 * Response DTO for worksheet question operations
 */
export class WorksheetQuestionResponseDto {
  @ApiProperty({ description: 'Operation success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiPropertyOptional({ description: 'Affected question IDs', type: [String] })
  affectedQuestionIds?: string[];

  @ApiPropertyOptional({ description: 'Updated worksheet total questions' })
  totalQuestions?: number;

  @ApiPropertyOptional({ description: 'Operation timestamp' })
  timestamp?: Date;

  @ApiPropertyOptional({ description: 'Additional operation data' })
  data?: any;
}
